---
export interface Props {
	isCorrect?: boolean;
}

const { isCorrect } = Astro.props as Props;
---

<li>
	<label>
		<input type="radio" disabled data-is-correct={isCorrect} /><span><slot /></span>
		{
			Astro.slots.has('feedback') && (
				<template>
					<slot name="feedback" />
				</template>
			)
		}
	</label>
</li>

<style>
	label {
		display: grid;
		grid-template-columns: auto 1fr;
		gap: 1rem;
		align-items: center;
		border-radius: 1rem;
		margin-inline: -0.5rem;
		padding: 0.5rem;
		cursor: pointer;
	}

	label:hover {
		background-color: var(--sl-color-accent-low);
	}

	input[type='radio'] {
		-webkit-appearance: none;
		appearance: none;
		cursor: pointer;
	}

	input[type='radio'] ~ * {
		color: var(--sl-color-text-accent);
	}

	input[type='radio']:checked ~ * {
		color: var(--sl-color-white);
	}

	input[type='radio']:focus::after {
		outline: 3px solid var(--sl-color-text-accent);
		outline-offset: 0.5rem;
	}

	input[type='radio']:focus,
	input[type='radio']:not(:focus-visible)::after {
		outline: none;
	}

	input[type='radio']::after {
		display: block;
		content: '';
		text-align: center;
		line-height: 1.5;
		width: 1.5em;
		height: 1.5em;
		font-size: 1.25em;
		font-weight: 900;
		border: 2px solid var(--sl-color-accent-high);
		border-radius: 100%;
		color: var(--sl-color-white);
		box-shadow: -3px 3px var(--theme-shade-subtle);
	}

	input[type='radio']:checked::after {
		color: hsl(var(--color-base-white), 100%);
		background-image: radial-gradient(var(--sl-color-white) 50%, transparent 0%, transparent);
		border-color: var(--sl-color-white);
	}

	@media (forced-colors: active) {
		input[type='radio']:checked::after {
			background-color: Highlight;
		}
	}
</style>