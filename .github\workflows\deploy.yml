name: Deploy to GitHub Pages

on:
  push:
    branches:
      - main
  pull_request:
    types: [closed]

jobs:
  build:
    if: github.event.pull_request.merged == true || github.event_name == 'push'
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '22.11.0'

      - name: Install dependencies
        run: |
          npm install -g pnpm
          pnpm install

      - name: Build project
        run: pnpm run build

      - name: Deploy to GitHub Pages
        uses: peaceiris/actions-gh-pages@v3
        with:
          github_token: ${{ secrets.GH_TOKEN }}
          publish_dir: ./dist
          keep_files: CNAME
