---
import { getImage } from 'astro:assets';
import HoustonGrad from '../assets/houston_grad.png';

const { src: houstonGrad } = await getImage({ src: HoustonGrad });

export interface Props {
	icon?: 'puzzle-piece' | 'question-mark' | 'check-list';
}

const { icon } = Astro.props;
---

<div
	class={`highlight-box content ${icon}`}
	style={icon === 'question-mark' ? `--box-icon-url: url('${houstonGrad}');` : ''}
>
	<section>
		<slot />
	</section>
</div>

<style>
	:global(section > *) + .highlight-box {
		margin: 4rem 0;
	}

	.highlight-box {
		position: relative;
		padding-block: 2rem;
		padding-inline-start: 2rem;
		padding-inline-end: 4rem;
		border: 1px solid var(--sl-color-purple);
		background-color: var(--sl-color-purple-low);
		color: var(--sl-color-purple-high);
		margin-top: 2.5rem !important;
	}

	.highlight-box.check-list {
		border: 1px solid var(--sl-color-green);
		background-color: var(--sl-color-green-low);
	}
	@supports (mask: var(--box-icon-url) no-repeat center bottom / 100%) or
		(-webkit-mask: var(--box-icon-url) no-repeat center bottom / 100%) {
		.highlight-box::before {
			content: '';
			position: absolute;
			top: -2.5rem;
			inset-inline-end: 1.5rem;
			width: 8rem;
			height: 8rem;
			background: var(--box-icon-url) no-repeat center center / 100%;
		}
	}

	.highlight-box :global(ol:not(ol ol)) {
		padding-left: 1.5rem;
	}

	.highlight-box :global(details) {
		--sl-details-border-color: var(--sl-color-purple);
		--sl-details-border-color--hover: var(--sl-color-purple-high);
	}

	@media (max-width: 50em) {
		.highlight-box::before {
			width: 5rem;
			height: 5rem;
		}
	}
</style>

<style is:global>
	.highlight-box.question-mark .sl-heading-wrapper:first-child {
		margin-inline-end: 6rem !important;
	}
	@media (max-width: 50em) {
		.highlight-box.question-mark .sl-heading-wrapper:first-child {
			margin-inline-end: 3rem !important;
		}
	}
</style>