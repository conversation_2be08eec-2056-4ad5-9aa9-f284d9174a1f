---
title: 考研扫盲篇
description: 学硕vs专硕选择、2025年考研新变化、备考时间线规划
---

## 考研扫盲篇 📚

### 2.1 学硕 vs 专硕怎么选？

| 对比维度 | 学术型硕士             | 专业型硕士               |
| -------- | ---------------------- | ------------------------ |
| 培养目标 | 学术研究               | 应用实践                 |
| 学制     | 3年                    | 2-3年                    |
| 学费     | 较低（8000-12000/年）  | 较高（10000-20000+/年）  |
| 考试难度 | 英语一、数学一，难度大 | 英语二、数学二，难度适中 |
| 就业方向 | 科研机构、高校         | 企业、公司               |

✨ **小建议**：想进大厂或就业的同学优先考虑专硕；对科研有兴趣、想读博的同学选择学硕。

### 2.2 2025年考研新变化

- **408统考科目**：数据结构、计算机组成原理、操作系统、计算机网络
- **院校自主命题**：部分学校如西南大学考808计算机专业基础综合
- **新增人工智能专业**：多所高校开设人工智能硕士点，如中央财经大学
- **推免比例提高**：985/211院校推免生占比可达50%以上

### 2.3 备考时间线规划

- **大一**：打好数学、英语基础，了解考研信息
- **大二**：开始专业课学习，确定目标院校
- **大三上学期**：系统复习数学和专业课
- **大三下学期**：开始刷题，参加暑期夏令营
- **大四上学期**：强化复习，参加模拟考试
- **12月底**：初试
- **次年3-4月**：复试、调剂

### 2.4 备考资源与方法

- **官方渠道**：目标院校研究生院官网、研招网
- **参考书目**：《数据结构》严蔚敏、《操作系统概念》Abraham Silberschatz
- **辅导资料**：王道论坛系列、天勤计算机考研系列
- **复习方法**：
  1. 制定详细计划，每日学习6-8小时
  2. 重视基础，不要盲目刷题
  3. 定期模拟测试，查漏补缺
  4. 加入考研社群，互相鼓励

⚠️ **注意**：2025年部分院校开始卡学历，如TP-Link只允许985学生进入面试，双非同学要提前做好准备。
